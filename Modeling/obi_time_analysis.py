import pandas as pd
import numpy as np
import glob
import matplotlib.pyplot as plt
from statsmodels.stats.diagnostic import acorr_ljungbox

# -----------------------------------------------------------------------------
# 1. Load all orderbook feather files
# -----------------------------------------------------------------------------
files = glob.glob("../Data_Fetching/kraken_data/BTC_USD_orderbook_*.feather")
print("Found files:", files)
dfs = [pd.read_feather(f) for f in files]
df = pd.concat(dfs, ignore_index=True)

# -----------------------------------------------------------------------------
# 2. Fixed parameters: cutoff (±1%) and segment_size (40 levels)
# -----------------------------------------------------------------------------
cutoff = 0.01          # ±1% price boundary
segment_size = 40      # divide into 40 price levels

# -----------------------------------------------------------------------------
# 3. For each snapshot, compute OBI by “±1% range divided into 40 levels”
# -----------------------------------------------------------------------------
snapshot_size = 1000   # each snapshot has 1000 rows
obi_list = []
bbo_list = []          # Best Bid Price
bba_list = []          # Best Ask Price
snapshot_times = []

for i in range(0, len(df), snapshot_size):
    # 3.1. Extract one snapshot and sort by Price
    snap = df.iloc[i:i+snapshot_size]
    snap_sorted = snap.sort_values('Price', ascending=True).reset_index(drop=True)

    # 3.2. Identify Best Bid and Best Ask for this snapshot
    #      Treat the first 500 rows as bids, last 500 as asks
    bids_all = snap_sorted.iloc[:snapshot_size//2].sort_values('Price', ascending=False)
    asks_all = snap_sorted.iloc[snapshot_size//2:].sort_values('Price', ascending=True)
    best_bid_price = bids_all.iloc[0]['Price']
    best_ask_price = asks_all.iloc[0]['Price']
    mid_price = 0.5 * (best_bid_price + best_ask_price)

    # 3.3. Compute lower and upper bounds: lower, upper
    lower = mid_price * (1 - cutoff)
    upper = mid_price * (1 + cutoff)

    # 3.4. Filter orders whose Price is within [lower, upper]
    mask_range = snap_sorted['Price'].between(lower, upper)
    snap_in_range = snap_sorted.loc[mask_range].copy()

    # If no orders fall within ±1%, set OBI to NaN
    if snap_in_range.empty:
        obi_list.append(np.nan)
        bbo_list.append(best_bid_price)
        bba_list.append(best_ask_price)
        snapshot_times.append(snap_sorted.iloc[0]['Time'])
        continue

    # 3.5. Divide this range into 40 equal-width bins
    bins = np.linspace(lower, upper, segment_size + 1)
    snap_in_range['Bin'] = pd.cut(
        snap_in_range['Price'],
        bins=bins,
        labels=False,
        include_lowest=True
    )

    # 3.6. Sum Size per bin, then fill missing bins with 0
    vol_by_bin = snap_in_range.groupby('Bin')['Size'].sum()
    vol_full = vol_by_bin.reindex(range(segment_size), fill_value=0).values  # always length 40

    # 3.7. First 20 bins correspond to bid side, last 20 bins to ask side
    half = segment_size // 2
    obi_n_layers = 5
    V_bid = vol_full[half - obi_n_layers:half].sum()
    V_ask = vol_full[half:half + obi_n_layers].sum()

    # 3.8. Compute OBI: (V_bid - V_ask) / (V_bid + V_ask)
    if (V_bid + V_ask) != 0:
        obi = (V_bid - V_ask) / (V_bid + V_ask)
    else:
        obi = np.nan

    # 3.9. Collect results
    obi_list.append(obi)
    bbo_list.append(best_bid_price)
    bba_list.append(best_ask_price)
    snapshot_times.append(snap_sorted.iloc[0]['Time'])

    # Optionally display first/last snapshot samples for verification
    if i == 0:
        print("First snapshot sample (±1% 40 levels):")
        print(snap_in_range.head())
    if i + snapshot_size >= len(df):
        print("Last snapshot sample (±1% 40 levels):")
        print(snap_in_range.tail())

# 3.10. Convert to Series and convert times to datetime
obi_series = pd.Series(obi_list)
snapshot_times = pd.to_datetime(snapshot_times)
mid_price_series = pd.Series(0.5 * (pd.Series(bbo_list) + pd.Series(bba_list)))
mid_price_series.index = pd.to_datetime(snapshot_times)

# # -----------------------------------------------------------------------------
# # 4. Plot: Moving Average OBI (example for Window=1 and Window=10)
# # -----------------------------------------------------------------------------
# plt.figure(figsize=(10, 6))
# ma_obi_1 = obi_series.rolling(window=1).mean()
# ma_obi_10 = obi_series.rolling(window=10).mean()
# plt.plot(snapshot_times, ma_obi_1, label='Window 1')
# plt.plot(snapshot_times, ma_obi_10, label='Window 10')
# plt.title(f"Moving Average OBI (Window 1 and 10, ±1% broken into {segment_size} levels)")
# plt.xlabel("Time")
# plt.ylabel("OBI (Moving Average)")
# plt.legend()
# plt.tight_layout()
# plt.show()

# # -----------------------------------------------------------------------------
# # 5. Autocorrelation & Ljung-Box Test
# # -----------------------------------------------------------------------------
# lags = [1, 2, 4, 8, 16, 32, 64, 128]
# window_list = [1, 5, 10]
# plt.figure(figsize=(8, 5))
# for window in window_list:
#     ma_obi = obi_series.rolling(window=window).mean()
#     acf_values = [ma_obi.autocorr(lag=lag) for lag in lags]
#     plt.scatter(lags, acf_values, label=f'Window {window}')
#     plt.plot(lags, acf_values, linestyle='--', alpha=0.7)
# plt.title('Autocorrelation of Moving-Averaged OBI (Different Window Sizes)')
# plt.xlabel('Lag')
# plt.ylabel('Autocorrelation')
# plt.xscale('log')
# plt.legend()
# plt.tight_layout()
# plt.show()

# for w in [1, 5, 10]:
#     obi_smoothed = obi_series.rolling(window=w).mean().dropna()
#     result = acorr_ljungbox(obi_smoothed, lags=[100], return_df=True)
#     print(f"Window {w}: p-value = {result['lb_pvalue'].values[0]}")

# -----------------------------------------------------------------------------
# 6. Correlation with Future Returns
# -----------------------------------------------------------------------------
lags_dict = {
    '1min':   6,     #  1 minute = 6 snapshots
    '3min':   18,    #  3 minutes = 18 snapshots
    '5min':   30,    #  5 minutes = 30 snapshots
    '10min':  60,    # 10 minutes = 60 snapshots
    '30min':  180,   # 30 minutes = 180 snapshots
    '60min':  360,   # 60 minutes = 360 snapshots
    '120min': 720,   #120 minutes = 720 snapshots
    '180min': 1080   #180 minutes =1080 snapshots
}
window_list = [1, 5, 10, 20, 50, 100]  # windows for moving average (in number of snapshots)
corr_matrix = {w: {} for w in window_list}

# Precompute future returns (as before)
df_returns = pd.DataFrame(index=mid_price_series.index)
for label, lag_val in lags_dict.items():
    shifted = mid_price_series.shift(-lag_val)
    df_returns[label] = shifted / mid_price_series - 1

# For each window, compute moving-average OBI and correlations at each lag
for W in window_list:
    # Compute moving average of OBI over the past W snapshots (including current)
    obi_smoothed = obi_series.rolling(window=W).mean()

    for label, lag_val in lags_dict.items():
        # Align smoothed OBI and future return: drop the last lag_val to avoid out-of-bounds
        obi_aligned = obi_smoothed.iloc[:-lag_val].reset_index(drop=True)
        ret_aligned = df_returns[label].iloc[:-lag_val].reset_index(drop=True)
        tmp = pd.DataFrame({'obi': obi_aligned, 'return': ret_aligned}).dropna()
        corr_matrix[W][label] = tmp['obi'].corr(tmp['return'])

# Print correlation results for each window
for W in window_list:
    print(f"Window={W}:")
    for label in lags_dict.keys():
        corr_val = corr_matrix[W][label]
        print(f"  {label:<6} correlation = {corr_val:.4f}")
    print()

# Plot correlation vs. lag for each moving-average window
plt.figure(figsize=(10, 6))
for W in window_list:
    labels = list(lags_dict.keys())
    values = [corr_matrix[W][lbl] for lbl in labels]
    plt.plot(labels, values, marker='o', label=f'Window={W}')
plt.xticks(rotation=45)
plt.ylabel("Pearson Correlation")
plt.title("Correlation between MA-OBI(t) and Future Returns at Various Lags")
plt.legend()
plt.tight_layout()
plt.show()

# -----------------------------------------------------------------------------
# 7. Heatmap: Correlation for Various obi_n_layers and Lags (with annotations)
# -----------------------------------------------------------------------------
# Define the layers (odd numbers from 1 to 19)
layers = list(range(1, 20, 2))  # [1, 3, 5, ..., 19]

# Extract lag labels and their numeric values from lags_dict (preserves order)
lag_labels = list(lags_dict.keys())          # ['1min', '3min', ..., '180min']
lag_vals   = list(lags_dict.values())        # [6, 18, 30, ..., 1080]

# # Prepare a matrix to hold correlation values:
# # rows correspond to lag_vals (in the same order as lag_labels),
# # columns correspond to layers
# corr_matrix2 = np.zeros((len(lag_vals), len(layers)))

# # For each obi_n_layers, recompute obi_series and then compute correlation at each lag
# for j, obi_n in enumerate(layers):
#     # Recompute OBI series for this obi_n_layers
#     obi_list_n = []
#     for i in range(0, len(df), snapshot_size):
#         snap = df.iloc[i:i+snapshot_size]
#         snap_sorted = snap.sort_values('Price', ascending=True).reset_index(drop=True)

#         bids_all = snap_sorted.iloc[:snapshot_size//2].sort_values('Price', ascending=False)
#         asks_all = snap_sorted.iloc[snapshot_size//2:].sort_values('Price', ascending=True)
#         best_bid_price = bids_all.iloc[0]['Price']
#         best_ask_price = asks_all.iloc[0]['Price']
#         mid_price = 0.5 * (best_bid_price + best_ask_price)

#         lower = mid_price * (1 - cutoff)
#         upper = mid_price * (1 + cutoff)

#         mask_range = snap_sorted['Price'].between(lower, upper)
#         snap_in_range = snap_sorted.loc[mask_range].copy()

#         if snap_in_range.empty:
#             obi_list_n.append(np.nan)
#             continue

#         bins = np.linspace(lower, upper, segment_size + 1)
#         snap_in_range['Bin'] = pd.cut(
#             snap_in_range['Price'],
#             bins=bins,
#             labels=False,
#             include_lowest=True
#         )

#         vol_by_bin = snap_in_range.groupby('Bin')['Size'].sum()
#         vol_full = vol_by_bin.reindex(range(segment_size), fill_value=0).values

#         half = segment_size // 2
#         if obi_n > half:
#             raise ValueError(f"obi_n_layers ({obi_n}) cannot exceed half ({half})")
#         V_bid = vol_full[half - obi_n:half].sum()
#         V_ask = vol_full[half:half + obi_n].sum()

#         if (V_bid + V_ask) != 0:
#             obi_n_value = (V_bid - V_ask) / (V_bid + V_ask)
#         else:
#             obi_n_value = np.nan

#         obi_list_n.append(obi_n_value)

#     # Convert to Series and align index with snapshot_times
#     obi_series_n = pd.Series(obi_list_n, index=pd.to_datetime(snapshot_times))

#     # For each lag, compute Pearson correlation between obi_series_n and future returns
#     for i, lag_val in enumerate(lag_vals):
#         obi_aligned = obi_series_n.iloc[:-lag_val].reset_index(drop=True)
#         ret_aligned = df_returns.iloc[:-lag_val, :][lag_labels[i]].reset_index(drop=True)
#         tmp = pd.DataFrame({'obi': obi_aligned, 'return': ret_aligned}).dropna()
#         corr_matrix2[i, j] = tmp['obi'].corr(tmp['return'])

# # Plot heatmap with annotations
# plt.figure(figsize=(10, 8))
# im = plt.imshow(corr_matrix2,
#                 origin='lower',
#                 aspect='auto',
#                 cmap='RdBu_r',
#                 vmin=-1, vmax=1)

# # Set tick locations and labels
# plt.xticks(ticks=np.arange(len(layers)), labels=layers)
# plt.yticks(ticks=np.arange(len(lag_labels)), labels=lag_labels)

# plt.xlabel("obi_n_layers")
# plt.ylabel("Lag")
# plt.title("Heatmap: Correlation between OBI(t; layers) and Return(t + lag)")

# # Add colorbar
# cbar = plt.colorbar(im, label="Pearson Correlation")

# # Annotate each cell with its correlation value (formatted to two decimals)
# for i in range(len(lag_labels)):
#     for j in range(len(layers)):
#         val = corr_matrix2[i, j]
#         # Choose white text for strong colors, black otherwise
#         text_color = 'white' if abs(val) > 0.5 else 'black'
#         plt.text(j, i, f"{val:.2f}",
#                  ha='center', va='center',
#                  color=text_color, fontsize=9)

# plt.tight_layout()
# plt.show()

# -----------------------------------------------------------------------------
# 8. Heatmap: Correlation for obi_n_layers = 19 across Moving-Average Windows and Lags
# -----------------------------------------------------------------------------

# First, recompute OBI series using obi_n_layers = 19
obi_n_fixed = 1
obi_list_fixed = []

for i in range(0, len(df), snapshot_size):
    snap = df.iloc[i:i+snapshot_size]
    snap_sorted = snap.sort_values('Price', ascending=True).reset_index(drop=True)

    bids_all = snap_sorted.iloc[:snapshot_size//2].sort_values('Price', ascending=False)
    asks_all = snap_sorted.iloc[snapshot_size//2:].sort_values('Price', ascending=True)
    best_bid_price = bids_all.iloc[0]['Price']
    best_ask_price = asks_all.iloc[0]['Price']
    mid_price = 0.5 * (best_bid_price + best_ask_price)

    lower = mid_price * (1 - cutoff)
    upper = mid_price * (1 + cutoff)

    mask_range = snap_sorted['Price'].between(lower, upper)
    snap_in_range = snap_sorted.loc[mask_range].copy()

    if snap_in_range.empty:
        obi_list_fixed.append(np.nan)
        continue

    bins = np.linspace(lower, upper, segment_size + 1)
    snap_in_range['Bin'] = pd.cut(
        snap_in_range['Price'],
        bins=bins,
        labels=False,
        include_lowest=True
    )

    vol_by_bin = snap_in_range.groupby('Bin')['Size'].sum()
    vol_full = vol_by_bin.reindex(range(segment_size), fill_value=0).values

    half = segment_size // 2
    if obi_n_fixed > half:
        raise ValueError(f"obi_n_layers ({obi_n_fixed}) cannot exceed half ({half})")

    V_bid = vol_full[half - obi_n_fixed:half].sum()
    V_ask = vol_full[half:half + obi_n_fixed].sum()

    if (V_bid + V_ask) != 0:
        obi_value_fixed = (V_bid - V_ask) / (V_bid + V_ask)
    else:
        obi_value_fixed = np.nan

    obi_list_fixed.append(obi_value_fixed)

# Convert to Series and align index with snapshot_times
obi_series_fixed = pd.Series(obi_list_fixed, index=pd.to_datetime(snapshot_times))

# Define moving-average windows (in number of snapshots)
window_list = [1, 5, 10, 20, 50, 100]

# Prepare a matrix to hold correlation values:
# rows correspond to lag_vals, columns correspond to window_list
corr_matrix3 = np.zeros((len(lag_vals), len(window_list)))

# For each moving-average window, compute correlations with returns at each lag
for j, W in enumerate(window_list):
    # Compute moving average of OBI over the past W snapshots (backward-looking)
    obi_smoothed_fixed = obi_series_fixed.rolling(window=W).mean()

    for i, lag_val in enumerate(lag_vals):
        obi_aligned = obi_smoothed_fixed.iloc[:-lag_val].reset_index(drop=True)
        ret_aligned = df_returns.iloc[:-lag_val, :][lag_labels[i]].reset_index(drop=True)
        tmp = pd.DataFrame({'obi': obi_aligned, 'return': ret_aligned}).dropna()
        corr_matrix3[i, j] = tmp['obi'].corr(tmp['return'])

# Plot heatmap with annotations
plt.figure(figsize=(8, 6))
im2 = plt.imshow(corr_matrix3,
                 origin='lower',
                 aspect='auto',
                 cmap='RdBu_r',
                 vmin=-1, vmax=1)

# Set tick locations and labels
plt.xticks(ticks=np.arange(len(window_list)), labels=window_list)
plt.yticks(ticks=np.arange(len(lag_labels)), labels=lag_labels)

plt.xlabel("Moving-Average Window (number of snapshots)")
plt.ylabel("Lag")
plt.title(f"Heatmap: Correlation (obi_n_layers={obi_n_fixed}) between MA-OBI and Return")

# Add colorbar
cbar2 = plt.colorbar(im2, label="Pearson Correlation")

# Annotate each cell with its correlation value (formatted to two decimals)
for i in range(len(lag_labels)):
    for j in range(len(window_list)):
        val = corr_matrix3[i, j]
        text_color = 'white' if abs(val) > 0.5 else 'black'
        plt.text(j, i, f"{val:.2f}",
                 ha='center', va='center',
                 color=text_color, fontsize=9)

plt.tight_layout()
plt.show()

# -----------------------------------------------------------------------------
# 9. Spectral Analysis (FFT)
# -----------------------------------------------------------------------------
# obi = obi_series.dropna().values
# n = len(obi)
# dt = 10.0  # each snapshot is 10 seconds apart
# obi_fft = np.fft.fft(obi)
# freqs = np.fft.fftfreq(n, d=dt)
# positive_freqs = freqs[:n // 2]
# power = np.abs(obi_fft[:n // 2])**2

# plt.figure(figsize=(10, 5))
# plt.plot(positive_freqs, power)
# plt.title('Power Spectrum of OBI')
# plt.xlabel('Frequency (Hz)')
# plt.ylabel('Power')
# plt.grid(True)
# plt.tight_layout()
# plt.show()