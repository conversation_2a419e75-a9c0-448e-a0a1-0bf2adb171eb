import pandas as pd
import numpy as np
import glob
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import (
    accuracy_score,
    mean_squared_error,
    r2_score,
)
import warnings
from typing import Dict, List
from datetime import timedelta
from tqdm import tqdm

warnings.filterwarnings("ignore")


def load_trading_pair_data(pair: str, data_dir="../Data_fetching/Richard_data"):
    """Load and combine all trade data for a specific trading pair"""
    data_path = Path(data_dir)
    trade_files = glob.glob(str(data_path / f"{pair}_trades_*.feather"))

    if not trade_files:
        raise FileNotFoundError(f"No trade files found for {pair} in {data_dir}")

    print(f"Found {len(trade_files)} trade files for {pair}")

    # Load and combine all files
    dfs = []
    for file in trade_files:
        df = pd.read_feather(file)
        dfs.append(df)

    combined_df = pd.concat(dfs, ignore_index=True)

    # Convert time to datetime and sort
    combined_df["Time"] = pd.to_datetime(combined_df["Time"], errors="coerce")
    combined_df = combined_df.dropna(subset=["Time"])
    combined_df = combined_df.sort_values("Time").reset_index(drop=True)

    return combined_df


def calculate_mid_price_rolling(
    df: pd.DataFrame, window_size: int = 100
) -> pd.DataFrame:
    """Calculate rolling mid price for layering"""
    df = df.copy()
    df["MidPrice"] = df["Price"].rolling(window=window_size, center=True).median()
    df["MidPrice"] = df["MidPrice"].fillna(method="bfill").fillna(method="ffill")
    return df


def create_price_layers(
    df: pd.DataFrame, num_layers: int = 40, layer_width_pct: float = 0.01
) -> pd.DataFrame:
    """
    Create price layers around mid price. Each layer is ±1% from mid price.
    Layer 0: mid_price ± 0.5%
    Layer 1: mid_price ± 1.5%
    ...
    Layer 39: mid_price ± 39.5%
    """
    df = df.copy()

    # Calculate which layer each trade belongs to
    price_deviation = (df["Price"] - df["MidPrice"]) / df["MidPrice"]

    # Assign layer based on absolute deviation
    abs_deviation = np.abs(price_deviation)
    df["Layer"] = np.floor(abs_deviation / layer_width_pct).astype(int)

    # Cap at maximum layer
    df["Layer"] = np.minimum(df["Layer"], num_layers - 1)

    # Add side information for layers (positive for above mid, negative for below)
    df["LayerSide"] = np.where(price_deviation >= 0, 1, -1)
    df["SignedLayer"] = df["Layer"] * df["LayerSide"]

    return df


def create_layered_features(
    df: pd.DataFrame, lookback_window: int = 300
) -> pd.DataFrame:
    """
    Create features based on trading activity in different price layers
    """
    df = df.copy()

    # Initialize feature columns
    num_layers = df["Layer"].max() + 1

    # For each trade, calculate features from previous trades in the lookback window
    features = []

    print(f"Creating layered features with {num_layers} layers...")

    for i in tqdm(range(lookback_window, len(df))):
        current_time = df.iloc[i]["Time"]
        lookback_data = df.iloc[i - lookback_window : i]

        # Volume by layer
        layer_volumes = {}
        layer_counts = {}
        layer_buy_ratios = {}

        for layer in range(num_layers):
            layer_data = lookback_data[lookback_data["Layer"] == layer]

            if len(layer_data) > 0:
                layer_volumes[f"volume_layer_{layer}"] = layer_data["Size"].sum()
                layer_counts[f"count_layer_{layer}"] = len(layer_data)

                # Buy ratio in this layer
                buy_trades = layer_data[layer_data["Side"] == "buy"]
                layer_buy_ratios[f"buy_ratio_layer_{layer}"] = len(buy_trades) / len(
                    layer_data
                )
            else:
                layer_volumes[f"volume_layer_{layer}"] = 0.0
                layer_counts[f"count_layer_{layer}"] = 0
                layer_buy_ratios[f"buy_ratio_layer_{layer}"] = 0.5  # neutral

        # Aggregate features
        feature_row = {
            "index": i,
            "Time": current_time,
            "Price": df.iloc[i]["Price"],
            "Size": df.iloc[i]["Size"],
            "Side": df.iloc[i]["Side"],
            "MidPrice": df.iloc[i]["MidPrice"],
            "Layer": df.iloc[i]["Layer"],
            # Volume distribution across layers
            "total_volume": sum(layer_volumes.values()),
            "volume_concentration": max(layer_volumes.values())
            / (sum(layer_volumes.values()) + 1e-8),
            # Activity distribution
            "total_trades": sum(layer_counts.values()),
            "trade_concentration": max(layer_counts.values())
            / (sum(layer_counts.values()) + 1e-8),
            # Imbalance features
            "overall_buy_ratio": len(lookback_data[lookback_data["Side"] == "buy"])
            / len(lookback_data),
        }

        # Add individual layer features (top 10 layers only to keep manageable)
        for layer in range(min(10, num_layers)):
            feature_row[f"volume_layer_{layer}"] = layer_volumes.get(
                f"volume_layer_{layer}", 0.0
            )
            feature_row[f"count_layer_{layer}"] = layer_counts.get(
                f"count_layer_{layer}", 0
            )
            feature_row[f"buy_ratio_layer_{layer}"] = layer_buy_ratios.get(
                f"buy_ratio_layer_{layer}", 0.5
            )

        features.append(feature_row)

    return pd.DataFrame(features)


def create_multi_horizon_targets(
    df: pd.DataFrame, horizons: List[int] = None
) -> pd.DataFrame:
    """
    Create target variables for multiple prediction horizons (10s, 20s, ..., 200s)
    """
    if horizons is None:
        horizons = list(range(10, 210, 10))  # 10s, 20s, ..., 200s

    df = df.copy()

    print(f"Creating targets for horizons: {horizons} seconds")

    for horizon in horizons:
        # Initialize target columns
        df[f"future_side_{horizon}s"] = None
        df[f"future_size_{horizon}s"] = 0.0
        df[f"future_buy_volume_{horizon}s"] = 0.0
        df[f"future_sell_volume_{horizon}s"] = 0.0
        df[f"future_trade_count_{horizon}s"] = 0

        for i in range(len(df) - 1):
            current_time = df.iloc[i]["Time"]
            future_time = current_time + timedelta(seconds=horizon)

            # Find trades in the future window
            future_mask = (df["Time"] > current_time) & (df["Time"] <= future_time)
            future_trades = df[future_mask]

            if len(future_trades) > 0:
                # Next trade side and size
                next_trade = future_trades.iloc[0]
                df.loc[i, f"future_side_{horizon}s"] = next_trade["Side"]
                df.loc[i, f"future_size_{horizon}s"] = next_trade["Size"]

                # Aggregate statistics for the horizon
                buy_trades = future_trades[future_trades["Side"] == "buy"]
                sell_trades = future_trades[future_trades["Side"] == "sell"]

                df.loc[i, f"future_buy_volume_{horizon}s"] = buy_trades["Size"].sum()
                df.loc[i, f"future_sell_volume_{horizon}s"] = sell_trades["Size"].sum()
                df.loc[i, f"future_trade_count_{horizon}s"] = len(future_trades)

    return df


def train_layered_models(features_df: pd.DataFrame, horizons: List[int] = None) -> Dict:
    """
    Train multiple models for different prediction horizons
    """
    if horizons is None:
        horizons = list(range(10, 210, 10))

    # Prepare feature columns
    feature_cols = [
        col
        for col in features_df.columns
        if col.startswith(("volume_", "count_", "buy_ratio_", "total_", "overall_"))
    ]

    print(
        f"Training models with {len(feature_cols)} features for {len(horizons)} horizons"
    )

    X = features_df[feature_cols].fillna(0)

    # Scale features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    models = {"scaler": scaler, "feature_cols": feature_cols, "horizons": {}}

    for horizon in horizons:
        print(f"Training models for {horizon}s horizon...")

        # Side prediction (classification)
        side_col = f"future_side_{horizon}s"
        if side_col in features_df.columns:
            y_side = features_df[side_col].dropna()
            valid_indices = y_side.index
            X_side = X_scaled[valid_indices]

            if len(y_side) > 100:  # Minimum samples for training
                # Encode side labels
                le_side = LabelEncoder()
                y_side_encoded = le_side.fit_transform(y_side)

                X_train, X_test, y_train, y_test = train_test_split(
                    X_side, y_side_encoded, test_size=0.3, random_state=42
                )

                # Train side prediction models
                side_models = {}

                # Logistic Regression
                lr_side = LogisticRegression(random_state=42, max_iter=1000)
                lr_side.fit(X_train, y_train)
                lr_pred = lr_side.predict(X_test)
                lr_acc = accuracy_score(y_test, lr_pred)
                side_models["logistic"] = {"model": lr_side, "accuracy": lr_acc}

                # Random Forest
                rf_side = RandomForestClassifier(n_estimators=100, random_state=42)
                rf_side.fit(X_train, y_train)
                rf_pred = rf_side.predict(X_test)
                rf_acc = accuracy_score(y_test, rf_pred)
                side_models["random_forest"] = {"model": rf_side, "accuracy": rf_acc}

                models["horizons"][horizon] = {
                    "side_models": side_models,
                    "side_encoder": le_side,
                    "side_test_acc": {"logistic": lr_acc, "random_forest": rf_acc},
                }

        # Size prediction (regression) - conditional on side
        for side in ["buy", "sell"]:
            size_col = f"future_{side}_volume_{horizon}s"
            if size_col in features_df.columns:
                y_size = features_df[size_col].dropna()
                valid_indices = y_size.index
                X_size = X_scaled[valid_indices]

                print(
                    f"  {side} volume prediction: {len(y_size)} samples, mean={y_size.mean():.3f}, std={y_size.std():.3f}"
                )

                # Use log transformation for volume prediction to handle high variance
                # Add small constant to handle zeros
                y_size_log = np.log1p(y_size)  # log(1 + x) to handle zeros

                # Only predict if we have sufficient non-zero samples
                nonzero_mask = y_size > 0
                if nonzero_mask.sum() > 100:  # Increased minimum samples
                    X_size_filtered = X_size[nonzero_mask]
                    y_size_filtered = y_size_log[nonzero_mask]

                    # Check for sufficient variance in target
                    if y_size_filtered.std() > 0.01:  # Minimum variance threshold
                        X_train, X_test, y_train, y_test = train_test_split(
                            X_size_filtered,
                            y_size_filtered,
                            test_size=0.3,
                            random_state=42,
                        )

                        # Train size prediction models
                        size_models = {}

                        # Linear Regression with regularization
                        from sklearn.linear_model import Ridge

                        lr_size = Ridge(alpha=1.0)
                        lr_size.fit(X_train, y_train)
                        lr_pred = lr_size.predict(X_test)
                        lr_r2 = r2_score(y_test, lr_pred)
                        lr_mse = mean_squared_error(y_test, lr_pred)
                        size_models["linear"] = {
                            "model": lr_size,
                            "r2": lr_r2,
                            "mse": lr_mse,
                        }

                        # Random Forest with better parameters
                        rf_size = RandomForestRegressor(
                            n_estimators=50,
                            max_depth=10,
                            min_samples_split=10,
                            min_samples_leaf=5,
                            random_state=42,
                        )
                        rf_size.fit(X_train, y_train)
                        rf_pred = rf_size.predict(X_test)
                        rf_r2 = r2_score(y_test, rf_pred)
                        rf_mse = mean_squared_error(y_test, rf_pred)
                        size_models["random_forest"] = {
                            "model": rf_size,
                            "r2": rf_r2,
                            "mse": rf_mse,
                        }

                        if horizon not in models["horizons"]:
                            models["horizons"][horizon] = {}

                        models["horizons"][horizon][
                            f"{side}_volume_models"
                        ] = size_models
                        models["horizons"][horizon][f"{side}_volume_test_r2"] = {
                            "linear": lr_r2,
                            "random_forest": rf_r2,
                        }

                        print(
                            f"    {side} volume R²: Linear={lr_r2:.3f}, RF={rf_r2:.3f}"
                        )
                    else:
                        print(
                            f"    {side} volume: Insufficient variance in target (std={y_size_filtered.std():.6f})"
                        )
                else:
                    print(
                        f"    {side} volume: Insufficient non-zero samples ({nonzero_mask.sum()}/100 required)"
                    )

    return models


def get_available_trading_pairs(data_dir="../Data_fetching/Richard_data"):
    """Get all available trading pairs from the data directory"""
    data_path = Path(data_dir)
    trade_files = glob.glob(str(data_path / "*_trades_*.feather"))

    pairs = set()
    for file in trade_files:
        filename = Path(file).name
        # Extract pair name (e.g., "BTC_USD" from "BTC_USD_trades_1234.feather")
        pair = "_".join(filename.split("_")[:2])
        pairs.add(pair)

    print(f"Found {len(pairs)} trading pairs: {sorted(list(pairs))}")
    return sorted(list(pairs))


def analyze_layered_prediction(
    pair: str,
    data_dir="../Data_fetching/Richard_data",
    sample_size: int = 15000,
    num_layers: int = 40,
    lookback_window: int = 300,
    horizons: List[int] = None,
) -> Dict:
    """
    Complete layered prediction analysis for a trading pair
    """
    if horizons is None:
        horizons = list(range(10, 210, 10))  # 10s to 200s

    print(f"\n{'='*80}")
    print(f"LAYERED PREDICTION ANALYSIS FOR {pair}")
    print(f"Layers: {num_layers} (±1% each)")
    print(f"Lookback window: {lookback_window} trades")
    print(f"Prediction horizons: {horizons} seconds")
    print(f"{'='*80}")

    try:
        # Load data
        df = load_trading_pair_data(pair, data_dir)
        print(f"Loaded {len(df)} trades")

        # Sample if too large
        if len(df) > sample_size:
            print(f"Sampling {sample_size} trades from {len(df)} total")
            df = (
                df.sample(n=sample_size, random_state=42)
                .sort_values("Time")
                .reset_index(drop=True)
            )

        # Calculate mid price and layers
        print("Calculating mid price and price layers...")
        df = calculate_mid_price_rolling(df)
        df = create_price_layers(df, num_layers)

        # Create layered features
        print("Creating layered features...")
        features_df = create_layered_features(df, lookback_window)

        # Create multi-horizon targets
        print("Creating multi-horizon targets...")
        features_df = create_multi_horizon_targets(features_df, horizons)

        # Train models
        print("Training layered prediction models...")
        models = train_layered_models(features_df, horizons)

        return {
            "pair": pair,
            "models": models,
            "features_df": features_df,
            "num_layers": num_layers,
            "horizons": horizons,
            "data_size": len(df),
            "feature_size": len(features_df),
        }

    except Exception as e:
        print(f"Error in layered analysis: {str(e)}")
        import traceback

        traceback.print_exc()
        return None


def predict_future_trades(
    models: Dict, features_df: pd.DataFrame, horizon: int = 60
) -> Dict:
    """
    Make predictions for future trades using trained layered models
    """
    if horizon not in models["horizons"]:
        return {"error": f"No models trained for {horizon}s horizon"}

    horizon_models = models["horizons"][horizon]
    scaler = models["scaler"]
    feature_cols = models["feature_cols"]

    # Get the most recent features
    recent_features = features_df[feature_cols].iloc[-1:].fillna(0)
    X_scaled = scaler.transform(recent_features)

    predictions = {"horizon": horizon, "predictions": {}}

    # Side prediction
    if "side_models" in horizon_models:
        side_encoder = horizon_models["side_encoder"]

        for model_name, model_info in horizon_models["side_models"].items():
            model = model_info["model"]
            side_pred_encoded = model.predict(X_scaled)[0]
            side_pred = side_encoder.inverse_transform([side_pred_encoded])[0]
            side_prob = model.predict_proba(X_scaled)[0].max()

            predictions["predictions"][f"side_{model_name}"] = {
                "predicted_side": side_pred,
                "confidence": side_prob,
                "accuracy": model_info["accuracy"],
            }

    # Volume predictions (conditional on side)
    for side in ["buy", "sell"]:
        volume_key = f"{side}_volume_models"
        if volume_key in horizon_models:
            for model_name, model_info in horizon_models[volume_key].items():
                model = model_info["model"]
                volume_pred = model.predict(X_scaled)[0]

                predictions["predictions"][f"{side}_volume_{model_name}"] = {
                    "predicted_volume": max(0, volume_pred),
                    "r2_score": model_info["r2"],
                    "mse": model_info["mse"],
                }

    return predictions


def visualize_layered_results(result: Dict):
    """Create comprehensive visualizations for layered prediction results"""

    if not result or "models" not in result:
        print("No valid results to visualize")
        return

    models = result["models"]
    horizons = result["horizons"]

    # Create subplots
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f"Layered Prediction Analysis - {result['pair']}", fontsize=16)

    # 1. Side prediction accuracy across horizons
    ax1 = axes[0, 0]
    horizon_list = []
    lr_accs = []
    rf_accs = []

    for horizon in sorted(horizons):
        if (
            horizon in models["horizons"]
            and "side_test_acc" in models["horizons"][horizon]
        ):
            horizon_list.append(horizon)
            lr_accs.append(
                models["horizons"][horizon]["side_test_acc"].get("logistic", 0)
            )
            rf_accs.append(
                models["horizons"][horizon]["side_test_acc"].get("random_forest", 0)
            )

    if horizon_list:
        ax1.plot(horizon_list, lr_accs, "o-", label="Logistic Regression", color="blue")
        ax1.plot(horizon_list, rf_accs, "s-", label="Random Forest", color="green")
        ax1.set_xlabel("Prediction Horizon (seconds)")
        ax1.set_ylabel("Side Prediction Accuracy")
        ax1.set_title("Side Prediction Performance")
        ax1.legend()
        ax1.grid(True, alpha=0.3)

    # 2. Buy volume prediction R² across horizons
    ax2 = axes[0, 1]
    buy_lr_r2 = []
    buy_rf_r2 = []

    for horizon in sorted(horizons):
        if (
            horizon in models["horizons"]
            and "buy_volume_test_r2" in models["horizons"][horizon]
        ):
            buy_lr_r2.append(
                models["horizons"][horizon]["buy_volume_test_r2"].get("linear", 0)
            )
            buy_rf_r2.append(
                models["horizons"][horizon]["buy_volume_test_r2"].get(
                    "random_forest", 0
                )
            )

    if buy_lr_r2:
        ax2.plot(
            horizon_list[: len(buy_lr_r2)],
            buy_lr_r2,
            "o-",
            label="Linear Regression",
            color="blue",
        )
        ax2.plot(
            horizon_list[: len(buy_rf_r2)],
            buy_rf_r2,
            "s-",
            label="Random Forest",
            color="green",
        )
        ax2.set_xlabel("Prediction Horizon (seconds)")
        ax2.set_ylabel("Buy Volume R² Score")
        ax2.set_title("Buy Volume Prediction Performance")
        ax2.legend()
        ax2.grid(True, alpha=0.3)

    # 3. Sell volume prediction R² across horizons
    ax3 = axes[0, 2]
    sell_lr_r2 = []
    sell_rf_r2 = []

    for horizon in sorted(horizons):
        if (
            horizon in models["horizons"]
            and "sell_volume_test_r2" in models["horizons"][horizon]
        ):
            sell_lr_r2.append(
                models["horizons"][horizon]["sell_volume_test_r2"].get("linear", 0)
            )
            sell_rf_r2.append(
                models["horizons"][horizon]["sell_volume_test_r2"].get(
                    "random_forest", 0
                )
            )

    if sell_lr_r2:
        ax3.plot(
            horizon_list[: len(sell_lr_r2)],
            sell_lr_r2,
            "o-",
            label="Linear Regression",
            color="blue",
        )
        ax3.plot(
            horizon_list[: len(sell_rf_r2)],
            sell_rf_r2,
            "s-",
            label="Random Forest",
            color="green",
        )
        ax3.set_xlabel("Prediction Horizon (seconds)")
        ax3.set_ylabel("Sell Volume R² Score")
        ax3.set_title("Sell Volume Prediction Performance")
        ax3.legend()
        ax3.grid(True, alpha=0.3)

    # 4. Layer distribution analysis
    ax4 = axes[1, 0]
    if "features_df" in result:
        features_df = result["features_df"]
        layer_counts = []
        for layer in range(min(10, result["num_layers"])):
            count = (features_df["Layer"] == layer).sum()
            layer_counts.append(count)

        ax4.bar(range(len(layer_counts)), layer_counts, color="skyblue", alpha=0.7)
        ax4.set_xlabel("Price Layer")
        ax4.set_ylabel("Number of Trades")
        ax4.set_title("Trade Distribution Across Layers (Top 10)")
        ax4.grid(True, alpha=0.3)

    # 5. Model comparison heatmap
    ax5 = axes[1, 1]
    if horizon_list and lr_accs:
        comparison_data = np.array([lr_accs, rf_accs])
        im = ax5.imshow(comparison_data, cmap="RdYlGn", aspect="auto", vmin=0, vmax=1)
        ax5.set_xticks(range(len(horizon_list)))
        ax5.set_xticklabels([f"{h}s" for h in horizon_list])
        ax5.set_yticks([0, 1])
        ax5.set_yticklabels(["Logistic Reg", "Random Forest"])
        ax5.set_title("Side Prediction Accuracy Heatmap")

        # Add text annotations
        for i in range(2):
            for j in range(len(horizon_list)):
                ax5.text(
                    j,
                    i,
                    f"{comparison_data[i, j]:.2f}",
                    ha="center",
                    va="center",
                    color="black",
                    fontsize=8,
                )

        plt.colorbar(im, ax=ax5)

    # 6. Summary statistics
    ax6 = axes[1, 2]
    summary_text = f"""
    LAYERED PREDICTION SUMMARY

    Trading Pair: {result['pair']}
    Number of Layers: {result['num_layers']}
    Data Size: {result['data_size']:,} trades
    Feature Size: {result['feature_size']:,} samples

    Prediction Horizons: {len(horizons)}
    ({min(horizons)}s to {max(horizons)}s)

    Best Side Accuracy:
    LR: {max(lr_accs) if lr_accs else 0:.1%}
    RF: {max(rf_accs) if rf_accs else 0:.1%}

    Best Volume R²:
    Buy: {max(buy_rf_r2) if buy_rf_r2 else 0:.3f}
    Sell: {max(sell_rf_r2) if sell_rf_r2 else 0:.3f}
    """

    ax6.text(
        0.05,
        0.95,
        summary_text,
        transform=ax6.transAxes,
        fontsize=10,
        verticalalignment="top",
        bbox=dict(boxstyle="round", facecolor="lightblue"),
    )
    ax6.set_title("Analysis Summary")
    ax6.axis("off")

    plt.tight_layout()
    plt.savefig(
        f'layered_prediction_{result["pair"]}.png', dpi=300, bbox_inches="tight"
    )
    plt.show()


def main():
    """Main function to run layered prediction analysis"""

    print("🏗️ LAYERED TRADING PREDICTION ANALYSIS")
    print("=" * 80)

    # Get available trading pairs
    available_pairs = get_available_trading_pairs()
    print(f"Found {len(available_pairs)} trading pairs: {available_pairs}")

    # Analyze first few pairs as examples
    pairs_to_analyze = available_pairs[:5]  # Analyze first 5 pairs

    all_results = []

    for pair in pairs_to_analyze:
        print(f"\n🔄 Analyzing {pair}...")

        result = analyze_layered_prediction(
            pair=pair,
            sample_size=8000,  # Smaller sample for faster processing
            num_layers=40,
            lookback_window=200,
            horizons=[10, 30, 60, 120, 200],  # Selected horizons
        )

        if result:
            print(f"✅ {pair}: Analysis complete")
            all_results.append(result)

            # Create visualizations for the first result
            if len(all_results) == 1:
                print(f"Creating visualizations for {pair}...")
                visualize_layered_results(result)

            # Test prediction functionality
            if "features_df" in result and len(result["features_df"]) > 0:
                print(f"Testing prediction for {pair}...")
                prediction = predict_future_trades(
                    result["models"], result["features_df"], horizon=60
                )
                if "error" not in prediction:
                    print(f"Sample prediction: {prediction}")
        else:
            print(f"❌ {pair}: Analysis failed")

    if all_results:
        print(f"\n📊 ANALYSIS SUMMARY")
        print("=" * 80)
        print(f"Successfully analyzed {len(all_results)} pairs")

        # Save results summary
        summary_data = []
        for result in all_results:
            models = result["models"]
            for horizon in result["horizons"]:
                if horizon in models["horizons"]:
                    horizon_models = models["horizons"][horizon]

                    summary_row = {
                        "pair": result["pair"],
                        "horizon": horizon,
                        "num_layers": result["num_layers"],
                        "data_size": result["data_size"],
                    }

                    # Add model performance metrics
                    if "side_test_acc" in horizon_models:
                        summary_row["side_acc_lr"] = horizon_models[
                            "side_test_acc"
                        ].get("logistic", 0)
                        summary_row["side_acc_rf"] = horizon_models[
                            "side_test_acc"
                        ].get("random_forest", 0)

                    if "buy_volume_test_r2" in horizon_models:
                        summary_row["buy_vol_r2_lr"] = horizon_models[
                            "buy_volume_test_r2"
                        ].get("linear", 0)
                        summary_row["buy_vol_r2_rf"] = horizon_models[
                            "buy_volume_test_r2"
                        ].get("random_forest", 0)

                    if "sell_volume_test_r2" in horizon_models:
                        summary_row["sell_vol_r2_lr"] = horizon_models[
                            "sell_volume_test_r2"
                        ].get("linear", 0)
                        summary_row["sell_vol_r2_rf"] = horizon_models[
                            "sell_volume_test_r2"
                        ].get("random_forest", 0)

                    summary_data.append(summary_row)

        if summary_data:
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_csv("layered_prediction_results.csv", index=False)
            print(f"Results saved to 'layered_prediction_results.csv'")

            # Print best performers
            if "side_acc_rf" in summary_df.columns:
                best_side = summary_df.loc[summary_df["side_acc_rf"].idxmax()]
                print(
                    f"\n🏆 Best Side Prediction: {best_side['pair']} at {best_side['horizon']}s - {best_side['side_acc_rf']:.1%}"
                )

            if "buy_vol_r2_rf" in summary_df.columns:
                best_buy_vol = summary_df.loc[summary_df["buy_vol_r2_rf"].idxmax()]
                print(
                    f"🏆 Best Buy Volume Prediction: {best_buy_vol['pair']} at {best_buy_vol['horizon']}s - R²={best_buy_vol['buy_vol_r2_rf']:.3f}"
                )

            return all_results

    return None


if __name__ == "__main__":
    results = main()
