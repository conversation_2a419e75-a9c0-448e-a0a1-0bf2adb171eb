import pandas as pd
import numpy as np
import glob
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.linear_model import LogisticRegression

from sklearn.preprocessing import StandardScaler
from sklearn.metrics import (
    classification_report,
    confusion_matrix,
    accuracy_score,
    roc_auc_score,
)

import warnings
from typing import Dict, List, Tuple

warnings.filterwarnings("ignore")


def get_trading_pairs(data_dir="Data_Fetching/Richard_data"):
    """Get all available trading pairs with trades files"""
    data_path = Path(data_dir)
    trade_files = glob.glob(str(data_path / "*_trades_*.feather"))

    # Extract unique trading pairs
    pairs = set()
    for file in trade_files:
        filename = Path(file).name
        # Extract pair name (e.g., "BTC_USD" from "BTC_USD_trades_*.feather")
        pair = "_".join(filename.split("_")[:2])
        pairs.add(pair)

    return sorted(list(pairs))


def load_trading_pair_data(pair: str, data_dir="Data_Fetching/Richard_data"):
    """Load and combine all trade data for a specific trading pair"""
    data_path = Path(data_dir)
    trade_files = glob.glob(str(data_path / f"{pair}_trades_*.feather"))

    if not trade_files:
        raise FileNotFoundError(f"No trade files found for {pair} in {data_dir}")

    print(f"Found {len(trade_files)} trade files for {pair}")

    # Load and combine all files
    dfs = []
    for file in trade_files:
        df = pd.read_feather(file)
        dfs.append(df)

    combined_df = pd.concat(dfs, ignore_index=True)

    # Convert time to datetime
    combined_df["Time"] = pd.to_datetime(combined_df["Time"], errors="coerce")
    combined_df = combined_df.dropna(subset=["Time"])

    # Sort by time
    combined_df = combined_df.sort_values("Time").reset_index(drop=True)

    return combined_df


def create_orderbook_features(
    df: pd.DataFrame, window_sizes=[10, 50, 100]
) -> pd.DataFrame:
    """Create features from orderbook trade data to predict next trade side"""

    # Sort by time to ensure chronological order
    df = df.sort_values("Time").reset_index(drop=True)

    # Create time-based features
    df["Hour"] = df["Time"].dt.hour
    df["DayOfWeek"] = df["Time"].dt.dayofweek
    df["Minute"] = df["Time"].dt.minute

    # Create price and volume features
    df["LogPrice"] = np.log(df["Price"])
    df["LogSize"] = np.log(df["Size"])

    # Create binary side indicator (1 for buy, 0 for sell)
    df["SideBinary"] = (df["Side"] == "buy").astype(int)

    # Calculate features for different window sizes
    for window in window_sizes:
        # Price momentum features
        df[f"PriceMA_{window}"] = df["Price"].rolling(window=window).mean()
        df[f"PriceStd_{window}"] = df["Price"].rolling(window=window).std()
        df[f"PriceChange_{window}"] = df["Price"] - df[f"PriceMA_{window}"]
        df[f"PriceChangeRatio_{window}"] = (
            df[f"PriceChange_{window}"] / df[f"PriceMA_{window}"]
        )

        # Volume features
        df[f"VolumeMA_{window}"] = df["Size"].rolling(window=window).mean()
        df[f"VolumeStd_{window}"] = df["Size"].rolling(window=window).std()
        df[f"VolumeRatio_{window}"] = df["Size"] / df[f"VolumeMA_{window}"]

        # Order book imbalance features
        df[f"BuyRatio_{window}"] = df["SideBinary"].rolling(window=window).mean()
        df[f"SellRatio_{window}"] = 1 - df[f"BuyRatio_{window}"]
        df[f"OBI_{window}"] = (df[f"BuyRatio_{window}"] - df[f"SellRatio_{window}"]) / (
            df[f"BuyRatio_{window}"] + df[f"SellRatio_{window}"]
        )

        # Trade intensity features
        df[f"TradeCount_{window}"] = df["SideBinary"].rolling(window=window).count()

        # Price volatility
        df[f"Returns_{window}"] = df["Price"].pct_change(window)
        df[f"Volatility_{window}"] = (
            df[f"Returns_{window}"].rolling(window=window).std()
        )

    # Create lag features (previous trade characteristics)
    for lag in [1, 2, 3, 5, 10]:
        df[f"PriceLag_{lag}"] = df["Price"].shift(lag)
        df[f"SizeLag_{lag}"] = df["Size"].shift(lag)
        df[f"SideLag_{lag}"] = df["SideBinary"].shift(lag)

    # Create target variable (next trade side)
    df["NextSide"] = df["SideBinary"].shift(-1)

    # Remove rows with NaN values
    df = df.dropna()

    return df


def prepare_features_and_target(
    df: pd.DataFrame,
) -> Tuple[pd.DataFrame, pd.Series, List[str]]:
    """Prepare feature matrix and target variable"""

    # Define feature columns (exclude non-feature columns)
    exclude_cols = [
        "Time",
        "Price",
        "Size",
        "Side",
        "SideBinary",
        "NextSide",
        "LogPrice",
        "LogSize",
    ]
    feature_cols = [col for col in df.columns if col not in exclude_cols]

    X = df[feature_cols]
    y = df["NextSide"]

    return X, y, feature_cols


def train_logistic_regression(X: pd.DataFrame, y: pd.Series) -> Dict:
    """Train Logistic Regression model and return results"""

    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )

    # Scale features for models that need it
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    # Define models
    models = {
        "Logistic Regression": LogisticRegression(random_state=42, max_iter=1000),
    }

    results = {}

    for name, model in models.items():
        print(f"\nTraining {name}...")

        # Use scaled data for Logistic Regression
        model.fit(X_train_scaled, y_train)
        y_pred = model.predict(X_test_scaled)
        y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]

        # Calculate metrics
        accuracy = accuracy_score(y_test, y_pred)
        auc_score = roc_auc_score(y_test, y_pred_proba)

        # Cross-validation score
        cv_scores = cross_val_score(
            model, X_train_scaled, y_train, cv=5, scoring="accuracy"
        )

        results[name] = {
            "model": model,
            "accuracy": accuracy,
            "auc_score": auc_score,
            "cv_mean": cv_scores.mean(),
            "cv_std": cv_scores.std(),
            "y_test": y_test,
            "y_pred": y_pred,
            "y_pred_proba": y_pred_proba,
            "classification_report": classification_report(y_test, y_pred),
        }

        print(f"Accuracy: {accuracy:.4f}")
        print(f"AUC Score: {auc_score:.4f}")
        print(f"CV Score: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")

    return results, scaler


def analyze_trading_pair(
    pair: str, data_dir="Data_Fetching/Richard_data", sample_size=100000
) -> Dict:
    """Complete analysis for a single trading pair"""
    print(f"\n{'='*50}")
    print(f"Analyzing {pair}")
    print(f"{'='*50}")

    try:
        # Load data
        df = load_trading_pair_data(pair, data_dir)
        print(f"Loaded {len(df)} trades for {pair}")

        # Sample data if it's too large
        if len(df) > sample_size:
            print(f"Sampling {sample_size} trades from {len(df)} total trades")
            df = (
                df.sample(n=sample_size, random_state=42)
                .sort_values("Time")
                .reset_index(drop=True)
            )

        # Create features
        print("Creating features...")
        df_features = create_orderbook_features(df)
        print(f"Created features for {len(df_features)} samples")

        # Prepare data
        X, y, feature_cols = prepare_features_and_target(df_features)
        print(f"Using {len(feature_cols)} features")

        # Check class distribution
        class_dist = y.value_counts(normalize=True)
        print(
            f"Class distribution - Buy: {class_dist[1]:.3f}, Sell: {class_dist[0]:.3f}"
        )

        # Train model
        results, scaler = train_logistic_regression(X, y)

        return {
            "pair": pair,
            "data_size": len(df),
            "feature_size": len(df_features),
            "num_features": len(feature_cols),
            "class_distribution": class_dist.to_dict(),
            "model_results": results,
            "feature_names": feature_cols,
        }

    except Exception as e:
        print(f"Error analyzing {pair}: {str(e)}")
        return None


def main():
    """Main analysis function"""
    print("Side Prediction Analysis for Multiple Trading Pairs")
    print("=" * 60)

    # Get all available trading pairs
    pairs = get_trading_pairs()
    print(f"Found {len(pairs)} trading pairs: {pairs}")

    # Analyze each pair
    all_results = {}

    for pair in pairs[:3]:  # Limit to first 3 pairs for initial testing
        result = analyze_trading_pair(pair)
        if result:
            all_results[pair] = result

    # Summary comparison
    print(f"\n{'='*60}")
    print("SUMMARY COMPARISON")
    print(f"{'='*60}")

    summary_data = []
    for pair, result in all_results.items():
        for model_name, model_result in result["model_results"].items():
            summary_data.append(
                {
                    "Pair": pair,
                    "Model": model_name,
                    "Accuracy": model_result["accuracy"],
                    "AUC": model_result["auc_score"],
                    "CV_Mean": model_result["cv_mean"],
                    "CV_Std": model_result["cv_std"],
                }
            )

    summary_df = pd.DataFrame(summary_data)

    # Display results
    print("\nModel Performance Summary:")
    print(summary_df.round(4))

    # Save results
    summary_df.to_csv("side_prediction_results.csv", index=False)
    print("\nResults saved to 'side_prediction_results.csv'")

    return all_results, summary_df


def visualize_results(all_results: Dict, summary_df: pd.DataFrame):
    """Create visualizations for the analysis results"""

    # Set up the plotting style
    plt.style.use("default")
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))

    # 1. Model Performance Comparison
    ax1 = axes[0, 0]
    model_performance = summary_df.groupby("Model")[["Accuracy", "AUC"]].mean()
    model_performance.plot(kind="bar", ax=ax1)
    ax1.set_title("Average Model Performance Across All Pairs")
    ax1.set_ylabel("Score")
    ax1.legend()
    ax1.tick_params(axis="x", rotation=45)

    # 2. Pair Performance Comparison
    ax2 = axes[0, 1]
    pair_performance = summary_df.groupby("Pair")[["Accuracy", "AUC"]].mean()
    pair_performance.plot(kind="bar", ax=ax2)
    ax2.set_title("Average Performance by Trading Pair")
    ax2.set_ylabel("Score")
    ax2.legend()
    ax2.tick_params(axis="x", rotation=45)

    # 3. Accuracy Distribution
    ax3 = axes[1, 0]
    summary_df["Accuracy"].hist(bins=20, ax=ax3, alpha=0.7)
    ax3.set_title("Distribution of Model Accuracies")
    ax3.set_xlabel("Accuracy")
    ax3.set_ylabel("Frequency")

    # 4. AUC vs Accuracy Scatter
    ax4 = axes[1, 1]
    for model in summary_df["Model"].unique():
        model_data = summary_df[summary_df["Model"] == model]
        ax4.scatter(
            model_data["Accuracy"], model_data["AUC"], label=model, alpha=0.7, s=60
        )
    ax4.set_xlabel("Accuracy")
    ax4.set_ylabel("AUC Score")
    ax4.set_title("AUC vs Accuracy by Model")
    ax4.legend(bbox_to_anchor=(1.05, 1), loc="upper left")
    ax4.plot([0.5, 1], [0.5, 1], "k--", alpha=0.5)  # Diagonal line

    plt.tight_layout()
    plt.savefig(
        "modeling/side_prediction_analysis_results.png", dpi=300, bbox_inches="tight"
    )
    plt.show()

    # Create confusion matrices for best performing models
    fig, axes = plt.subplots(1, len(all_results), figsize=(5 * len(all_results), 4))
    if len(all_results) == 1:
        axes = [axes]

    for idx, (pair, result) in enumerate(all_results.items()):
        # Find best model for this pair
        best_model = max(
            result["model_results"].items(), key=lambda x: x[1]["accuracy"]
        )
        model_name, model_result = best_model

        # Create confusion matrix
        cm = confusion_matrix(model_result["y_test"], model_result["y_pred"])
        sns.heatmap(
            cm,
            annot=True,
            fmt="d",
            ax=axes[idx],
            xticklabels=["Sell", "Buy"],
            yticklabels=["Sell", "Buy"],
        )
        axes[idx].set_title(
            f'{pair} - {model_name}\nAccuracy: {model_result["accuracy"]:.3f}'
        )
        axes[idx].set_xlabel("Predicted")
        axes[idx].set_ylabel("Actual")

    plt.tight_layout()
    plt.savefig("modeling/confusion_matrices.png", dpi=300, bbox_inches="tight")
    plt.show()


def feature_importance_analysis(all_results: Dict):
    """Analyze feature importance across models and pairs"""

    importance_data = []

    for pair, result in all_results.items():
        for model_name, model_result in result["model_results"].items():
            model = model_result["model"]

            # Get feature importance based on model type
            if hasattr(model, "feature_importances_"):
                # Tree-based models
                importances = model.feature_importances_
            elif hasattr(model, "coef_"):
                # Linear models
                importances = np.abs(model.coef_[0])
            else:
                continue

            # Create importance dataframe
            for feature, importance in zip(result["feature_names"], importances):
                importance_data.append(
                    {
                        "Pair": pair,
                        "Model": model_name,
                        "Feature": feature,
                        "Importance": importance,
                    }
                )

    if importance_data:
        importance_df = pd.DataFrame(importance_data)

        # Get top features across all models
        top_features = (
            importance_df.groupby("Feature")["Importance"]
            .mean()
            .sort_values(ascending=False)
            .head(15)
        )

        plt.figure(figsize=(12, 8))
        top_features.plot(kind="barh")
        plt.title("Top 15 Most Important Features (Average Across All Models)")
        plt.xlabel("Average Importance")
        plt.tight_layout()
        plt.savefig("modeling/feature_importance.png", dpi=300, bbox_inches="tight")
        plt.show()

        return importance_df

    return None


def trading_strategy_simulation(all_results: Dict):
    """Simulate a simple trading strategy based on predictions"""

    strategy_results = {}

    for pair, result in all_results.items():
        # Use the best performing model
        best_model = max(
            result["model_results"].items(), key=lambda x: x[1]["accuracy"]
        )
        model_name, model_result = best_model

        # Get predictions and probabilities
        y_test = model_result["y_test"]
        y_pred_proba = model_result["y_pred_proba"]

        # Simple strategy: trade when confidence is high
        confidence_threshold = 0.6
        high_confidence_buy = y_pred_proba > confidence_threshold
        high_confidence_sell = y_pred_proba < (1 - confidence_threshold)

        # Calculate strategy metrics
        total_trades = len(y_test)
        confident_trades = np.sum(high_confidence_buy | high_confidence_sell)

        if confident_trades > 0:
            # Accuracy on high confidence trades
            confident_mask = high_confidence_buy | high_confidence_sell
            confident_predictions = (y_pred_proba[confident_mask] > 0.5).astype(int)
            confident_actual = y_test[confident_mask]
            confident_accuracy = accuracy_score(confident_actual, confident_predictions)

            strategy_results[pair] = {
                "model": model_name,
                "total_trades": total_trades,
                "confident_trades": confident_trades,
                "confidence_ratio": confident_trades / total_trades,
                "confident_accuracy": confident_accuracy,
            }

    # Display strategy results
    if strategy_results:
        print(f"\n{'='*60}")
        print("TRADING STRATEGY SIMULATION")
        print(f"{'='*60}")
        print(f"Strategy: Trade only when model confidence > {confidence_threshold}")

        for pair, metrics in strategy_results.items():
            print(f"\n{pair} ({metrics['model']}):")
            print(f"  Total test trades: {metrics['total_trades']}")
            print(f"  High confidence trades: {metrics['confident_trades']}")
            print(f"  Confidence ratio: {metrics['confidence_ratio']:.3f}")
            print(
                f"  Accuracy on confident trades: {metrics['confident_accuracy']:.3f}"
            )

    return strategy_results


if __name__ == "__main__":
    # Run main analysis
    results, summary = main()

    # Additional analysis
    if results:
        print("\nCreating visualizations...")
        visualize_results(results, summary)

        print("\nAnalyzing feature importance...")
        importance_df = feature_importance_analysis(results)

        print("\nSimulating trading strategy...")
        strategy_results = trading_strategy_simulation(results)

        print(f"\n{'='*60}")
        print("ANALYSIS COMPLETE")
        print(f"{'='*60}")
        print("Files created:")
        print("- modeling/side_prediction_results.csv")
        print("- modeling/side_prediction_analysis_results.png")
        print("- modeling/confusion_matrices.png")
        print("- modeling/feature_importance.png")
