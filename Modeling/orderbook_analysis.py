import pandas as pd
import numpy as np
import glob
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
import warnings

warnings.filterwarnings("ignore")


def load_orderbook_data(data_dir="Data_Fetching/Richard_data"):
    """Load and combine all BTC/USD orderbook data files"""
    data_path = Path(data_dir)
    orderbook_files = glob.glob(str(data_path / "BTC_USD_orderbook_*.feather"))

    if not orderbook_files:
        raise FileNotFoundError(f"No orderbook files found in {data_dir}")

    print(f"Found {len(orderbook_files)} orderbook files")

    # Load and combine all files
    dfs = []
    for file in orderbook_files:
        df = pd.read_feather(file)
        dfs.append(df)

    combined_df = pd.concat(dfs, ignore_index=True)

    # Use format='mixed' to handle different datetime formats
    combined_df["Time"] = pd.to_datetime(
        combined_df["Time"], format="mixed", errors="coerce"
    )

    # Drop rows with invalid timestamps
    combined_df = combined_df.dropna(subset=["Time"])

    return combined_df


def calculate_obi(df, window_size=100):
    """
    Calculate Order Book Imbalance (OBI)
    OBI = (bid_volume - ask_volume) / (bid_volume + ask_volume)
    """
    # Sort by time to ensure chronological order
    df = df.sort_values("Time")

    # Group by timestamp to separate each snapshot
    grouped = df.groupby("Time")

    results = []
    for time, group in grouped:
        # Separate bids and asks (bids have higher prices in the data)
        mid_price = group["Price"].median()
        bids = group[group["Price"] < mid_price]
        asks = group[group["Price"] >= mid_price]

        # Calculate volumes
        bid_volume = bids["Size"].sum()
        ask_volume = asks["Size"].sum()

        # Calculate OBI
        if (bid_volume + ask_volume) > 0:
            obi = (bid_volume - ask_volume) / (bid_volume + ask_volume)
        else:
            obi = 0

        results.append(
            {
                "Time": time,
                "MidPrice": mid_price,
                "BidVolume": bid_volume,
                "AskVolume": ask_volume,
                "OBI": obi,
            }
        )

    result_df = pd.DataFrame(results)

    # Calculate rolling features
    result_df["OBI_MA"] = result_df["OBI"].rolling(window=window_size).mean()
    result_df["OBI_std"] = result_df["OBI"].rolling(window=window_size).std()
    result_df["Volume_Ratio"] = result_df["BidVolume"] / result_df["AskVolume"]
    result_df["Volume_Ratio"].replace([np.inf, -np.inf], np.nan, inplace=True)
    result_df["Volume_Ratio_MA"] = (
        result_df["Volume_Ratio"].rolling(window=window_size).mean()
    )

    # Calculate future price changes (target variable)
    result_df["Future_Price"] = result_df["MidPrice"].shift(-window_size)
    result_df["Price_Change"] = result_df["Future_Price"] - result_df["MidPrice"]
    result_df["Price_Change_Pct"] = result_df["Price_Change"] / result_df["MidPrice"]

    return result_df.dropna()


def train_prediction_model(obi_df):
    """Train a simple linear regression model to predict price changes"""
    # Features and target
    features = ["OBI", "OBI_MA", "OBI_std", "Volume_Ratio", "Volume_Ratio_MA"]
    X = obi_df[features]
    y = obi_df["Price_Change_Pct"]

    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42
    )

    # Train model
    model = LinearRegression()
    model.fit(X_train, y_train)

    # Evaluate
    train_score = model.score(X_train, y_train)
    test_score = model.score(X_test, y_test)

    print(f"Model R² on training data: {train_score:.4f}")
    print(f"Model R² on test data: {test_score:.4f}")

    # Feature importance
    importance = pd.DataFrame({"Feature": features, "Coefficient": model.coef_})
    print("\nFeature importance:")
    print(importance.sort_values("Coefficient", ascending=False))

    return model, X_test, y_test


def visualize_results(obi_df, model, X_test, y_test):
    """Visualize OBI and price predictions"""
    # Create predictions
    y_pred = model.predict(X_test)

    # Plot OBI vs Price
    plt.figure(figsize=(12, 8))

    plt.subplot(2, 1, 1)
    plt.title("Order Book Imbalance (OBI) and Mid Price")
    plt.plot(obi_df["Time"], obi_df["OBI"], "b-", alpha=0.7, label="OBI")
    plt.legend(loc="upper left")
    plt.ylabel("OBI")

    ax2 = plt.twinx()
    ax2.plot(obi_df["Time"], obi_df["MidPrice"], "r-", label="Mid Price")
    ax2.set_ylabel("Price")
    ax2.legend(loc="upper right")

    # Plot actual vs predicted price changes
    plt.subplot(2, 1, 2)
    plt.title("Actual vs Predicted Price Changes")
    plt.scatter(y_test, y_pred, alpha=0.5)
    plt.xlabel("Actual Price Change %")
    plt.ylabel("Predicted Price Change %")

    # Add diagonal line for perfect predictions
    min_val = min(min(y_test), min(y_pred))
    max_val = max(max(y_test), max(y_pred))
    plt.plot([min_val, max_val], [min_val, max_val], "k--")

    plt.tight_layout()
    plt.savefig("orderbook_analysis_results.png")
    plt.show()


def main():
    # Load data
    print("Loading orderbook data...")
    orderbook_df = load_orderbook_data()
    print(f"Loaded {len(orderbook_df)} orderbook entries")
    print("Orderbook data sample:\n", orderbook_df.head(50))

    # Calculate OBI
    print("\nCalculating Order Book Imbalance...")
    obi_df = calculate_obi(orderbook_df)
    print(f"Generated {len(obi_df)} OBI data points")

    # Display sample data
    print("\nSample OBI data:")
    print(obi_df.head())

    # Train model
    print("\nTraining prediction model...")
    model, X_test, y_test = train_prediction_model(obi_df)

    # Visualize results
    print("\nVisualizing results...")
    visualize_results(obi_df, model, X_test, y_test)

    print("\nAnalysis complete. Results saved to 'orderbook_analysis_results.png'")


if __name__ == "__main__":
    main()
