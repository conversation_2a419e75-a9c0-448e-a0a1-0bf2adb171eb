import ccxt
import time
import datetime

exchange = ccxt.kraken({'enableRateLimit': True})
limit = 500  # Number of order book entries to fetch
orderbook_history = []  # Stores historical order book snapshots

# Dictionary to store last trade IDs for each trading pair
last_trade_ids = {}

def fetch_and_store(trading_pair='BTC/USD'):
    """
    Fetches and stores the current order book snapshot for a specified trading pair.
    
    Handles rate limits and retries automatically. Stores results in a 2D list
    where each inner list contains [Price, Size, Time].
    
    Args:
        trading_pair (str, optional): Trading pair to fetch (e.g. 'BTC/USD'). Defaults to 'BTC/USD'.
    
    Returns:
        list: 2D list of [Price, Size, Time] entries
    """
    success = False
    result = []
    
    while not success:
        try:
            # Generate timestamp with nanosecond precision
            ts = time.time_ns()
            dt = datetime.datetime.utcfromtimestamp(ts / 1e9)
            formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

            # Fetch order book with error handling
            orderbook = exchange.fetch_order_book(trading_pair, limit)
            
            # Process bids and asks into the desired format
            for bid in orderbook['bids']:
                # Each bid is [price, size]
                result.append([bid[0], bid[1], formatted_time])
                
            for ask in orderbook['asks']:
                # Each ask is [price, size]
                result.append([ask[0], ask[1], formatted_time])
            
            #print(f"Successfully captured {trading_pair} snapshot with {len(result)} entries")
            success = True  # Exit loop after successful fetch
            
            # Add to global history
            orderbook_history.extend(result)

        except ccxt.RateLimitExceeded:
            print("Rate limit hit - implementing backoff strategy")
            time.sleep(1)
        except Exception as e:
            print(f"Error fetching {trading_pair}: {str(e)}")
            time.sleep(1)
    
    return result

def fetch_trades(trading_pair='BTC/USD'):
    """
    Fetches and stores recent trades for a specified trading pair.
    Only fetches trades that are newer than the last seen trade.
    
    Handles rate limits and retries automatically. Stores results in a 2D list
    where each inner list contains [Price, Size, Time, Side].
    
    Args:
        trading_pair (str, optional): Trading pair to fetch (e.g. 'BTC/USD'). Defaults to 'BTC/USD'.
    
    Returns:
        list: 2D list of [Price, Size, Time, Side] entries
    """
    success = False
    result = []
    
    while not success:
        try:
            # Fetch recent trades without limit
            trades = exchange.fetch_trades(trading_pair)
            
            # Get the last trade ID we've seen for this pair
            last_id = last_trade_ids.get(trading_pair, None)
            
            # Process trades
            new_trades = []
            if last_id is None:
                # First time fetching, only get the most recent trade
                if trades:
                    new_trades = [trades[0]]  # Only get the most recent trade
                    last_trade_ids[trading_pair] = trades[0]['id']
            else:
                # Get all trades newer than last_id
                for trade in trades:
                    if trade['id'] > last_id:
                        new_trades.append(trade)
                # Update the last trade ID if we got any new trades
                if new_trades:
                    last_trade_ids[trading_pair] = new_trades[0]['id']
            
            # Process trades into the desired format
            for trade in new_trades:
                # Convert timestamp to datetime and format it
                trade_time = datetime.datetime.fromtimestamp(trade['timestamp'] / 1000)
                formatted_time = trade_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]  # Include milliseconds
                
                # Each trade contains: price, amount, timestamp, side (buy/sell)
                result.append([
                    trade['price'],
                    trade['amount'],
                    formatted_time,
                    trade['side']
                ])
            
            success = True  # Exit loop after successful fetch

        except ccxt.RateLimitExceeded:
            print("Rate limit hit - implementing backoff strategy")
            time.sleep(1)
        except Exception as e:
            print(f"Error fetching trades for {trading_pair}: {str(e)}")
            time.sleep(1)
    
    return result

if __name__ == "__main__":
    # Example usage patterns:
    orderbook_data = fetch_and_store()  # Default BTC/USD
    trades_data = fetch_trades()  # Default BTC/USD
    
    # Print sample of the data
    if orderbook_data:
        print("\nSample orderbook data format:")
        for i in range(min(5, len(orderbook_data))):
            print(orderbook_data[i])
            
    if trades_data:
        print("\nSample trades data format:")
        for i in range(min(5, len(trades_data))):
            print(trades_data[i])
            
    print(f"\nTotal orderbook entries captured: {len(orderbook_history)}")
    print(f"Total trades captured: {len(trades_data)}")
