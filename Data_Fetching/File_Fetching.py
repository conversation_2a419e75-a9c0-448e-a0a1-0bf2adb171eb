import pandas as pd
import time
import os
from multiprocessing import Process, Event

def Fetch_n_Store(ticker, default_fetch_time_interval, default_time_buffer, ram_limit, destination, fetch_function, live_trading = False):
    current_time_buffer = default_time_buffer
    current_fetch_time_interval = default_fetch_time_interval
    orderbook_hist = {ticker: []}
    trades_hist = {ticker: []}
    
    # Create separate destinations for orderbook and trades
    base_name = destination.replace('.feather', '')
    orderbook_dest = f"{base_name}_orderbook"
    trades_dest = f"{base_name}_trades"
    
    print(f"\nStarting data collection for {ticker}")
    print(f"RAM limit: {ram_limit}")
    
    while True:
        try: 
            # Get data from fetch_function
            data = fetch_function(ticker)
            if isinstance(data, tuple) and len(data) == 2:
                orderbook_snap, trades_snap = data
                orderbook_hist[ticker] += (orderbook_snap)
                trades_hist[ticker] += (trades_snap)
            else:
                orderbook_hist[ticker] += (data)

            # return data if live trading is in process for subsequent model run
            if live_trading:
                pass
            
            # check if order book reaches the RAM limit, will export then reduce the next data extraction interval
            if len(orderbook_hist[ticker]) >= ram_limit or len(trades_hist[ticker]) >= ram_limit:
                print(f"\n{ticker} - RAM limit reached!")
                print(f"Orderbook entries: {len(orderbook_hist[ticker])}")
                print(f"Trades entries: {len(trades_hist[ticker])}")
                
                start = time.time()
                timestamp = start
                
                if orderbook_hist[ticker]:
                    print(f"Saving {len(orderbook_hist[ticker])} orderbook records...")
                    pd.DataFrame(orderbook_hist[ticker], columns=['Price', 'Size', 'Time']).to_feather(f"{orderbook_dest}_{timestamp}.feather")
                    orderbook_hist = {ticker: []}
                
                if trades_hist[ticker]:
                    print(f"Saving {len(trades_hist[ticker])} trades records...")
                    pd.DataFrame(trades_hist[ticker], columns=['Price', 'Size', 'Time', 'Side']).to_feather(f"{trades_dest}_{timestamp}.feather")
                    trades_hist = {ticker: []}
                
                end = time.time()
                current_time_buffer = end - start
                current_fetch_time_interval -= current_time_buffer if current_fetch_time_interval > current_time_buffer else default_fetch_time_interval
                time.sleep(current_fetch_time_interval)               
            else:
                time.sleep(current_fetch_time_interval)
                current_fetch_time_interval = default_fetch_time_interval

        except Exception as e:
            print(f"Error: {e}")
            time.sleep(current_time_buffer)
            current_fetch_time_interval -= current_time_buffer if current_fetch_time_interval > current_time_buffer else default_fetch_time_interval
        current_time_buffer = default_time_buffer
            
        
        

def MultiTicker_Fetch_n_Store(tickers, fetch_time_interval, time_offset, ram_limit, destinations, fetch_function):
    start_event = Event()
    processes = [Process(target=Fetch_n_Store, 
                         args = (ticker, 
                                 fetch_time_interval, 
                                 time_offset, 
                                 ram_limit, 
                                 destination, 
                                 fetch_function)) for ticker, destination in zip(tickers, destinations)]

    for p in processes:
        p.start()

    time.sleep(1)  # optional delay to ensure all are ready
    start_event.set()  # release all workers simultaneously

    for p in processes:
        p.join()




