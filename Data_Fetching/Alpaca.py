from alpaca.data.historical import CryptoHistoricalDataClient
from alpaca.data.requests import CryptoLatestOrderbookRequest  # Correct class name
from alpaca.data.timeframe import TimeFrame


import pandas as pd
import pickle as pk

#
# Read Pickle File into Scrip for container
#

with open('BTC_USD_orderbook.pkl', 'rb') as file:
    price_size_df_old = pd.read_pickle(file)


#
# L2 Data Importing and Procesing
#


# Create client without authentication - no API keys needed
client = CryptoHistoricalDataClient()

# Request Bitcoin orderbook data
request_params = CryptoLatestOrderbookRequest(
    symbol_or_symbols=["BTC/USD"]
)

# Get latest orderbook data
orderbook = client.get_crypto_latest_orderbook(request_params)

# Print the orderbook data
print(orderbook)


#
# L2 Data Exporting
#

price_size_dict = {price_size.price: price_size.size for price_size in orderbook['BTC/USD'].asks}
price_size_dict.update({price_size.price: price_size.size for price_size in orderbook['BTC/USD'].bids})
price_size_df = pd.DataFrame(list(price_size_dict.items()), columns=['Price', 'Size'])
price_size_df = price_size_df.sort_values(by='Price', ascending=False).reset_index(drop=True)
price_size_df['Time'] = orderbook['BTC/USD'].timestamp

pd.concat([price_size_df_old, price_size_df]).to_pickle('BTC_USD_orderbook.pkl')
