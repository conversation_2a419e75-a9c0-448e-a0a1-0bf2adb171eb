import ccxt
import time
import datetime

exchange = ccxt.binanceus({
    'enableRateLimit': True,
    'rateLimit': 250,
})

limit = 5000

def fetch_deep_orderbook(trading_pair="BTC/USDT"):
    """
    Fetches a single real-time order book snapshot from Binance.US for the given trading pair.

    Args:
        trading_pair (str): The trading pair symbol (e.g., "BTC/USDT"). Defaults to "BTC/USDT".

    Returns:
        list: A list with one dictionary containing the timestamp, top bid/ask prices,
              number of bid/ask levels, and full bids/asks lists.
    """
    orderbook_history = []
    success = False
    
    while not success:
        try:
            ts = time.time_ns()
            dt = datetime.datetime.utcfromtimestamp(ts / 1e9)
            formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")

            orderbook = exchange.fetch_order_book(trading_pair, limit)
            
            snapshot = {
                'timestamp': formatted_time,
                'top_bid': orderbook['bids'][0][0] if orderbook['bids'] else None,
                'top_ask': orderbook['asks'][0][0] if orderbook['asks'] else None,
                'bid_levels': len(orderbook['bids']),
                'ask_levels': len(orderbook['asks']),
                'bids': orderbook['bids'],
                'asks': orderbook['asks']
            }
            
            orderbook_history.append(snapshot)
            print('Captured snapshot:', snapshot)
            success = True  # Exit after successful capture

        except ccxt.RateLimitExceeded:
            print("Rate limit exceeded - retrying in 5 seconds")
            time.sleep(5)
        except Exception as e:
            print(f"Error: {str(e)} - retrying in 5 seconds")
            time.sleep(5)
    
    return orderbook_history

if __name__ == "__main__":
    # Example usage: fetch default BTC/USDT
    result = fetch_deep_orderbook()
    print("\nSuccessfully captured depth snapshot for BTC/USDT.")

    # Example usage: fetch for another trading pair
    # result = fetch_deep_orderbook("ETH/USDT")
    # print("\nSuccessfully captured depth snapshot for ETH/USDT.")
