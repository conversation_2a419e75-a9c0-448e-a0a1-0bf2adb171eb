import pandas as pd
import glob
from pathlib import Path

def check_data(data_dir='kraken_data'):
    """檢查 BTC/USD 的數據"""
    data_path = Path(data_dir)
    
    # 獲取 BTC/USD 的文件
    orderbook_files = glob.glob(str(data_path / "BTC_USD_orderbook_*.feather"))
    trades_files = glob.glob(str(data_path / "BTC_USD_trades_*.feather"))
    
    print(f"\n找到 {len(orderbook_files)} 個 orderbook 文件和 {len(trades_files)} 個 trades 文件")
    
    # 檢查 orderbook 數據
    print("\n=== BTC/USD Orderbook 數據 ===")
    for file in orderbook_files[:3]:  # 只顯示前3個文件
        print(f"\n文件: {Path(file).name}")
        df = pd.read_feather(file)
        print(f"數據條數: {len(df)}")
        print("\n列名:", df.columns.tolist())
        print("\n數據類型:")
        print(df.dtypes)
        print("\n前5行數據:")
        print(df.head())
        print("\n數據統計:")
        print(df.describe())
        
        # 檢查時間範圍
        if 'Time' in df.columns:
            print("\n時間範圍:")
            print("最早時間:", df['Time'].min())
            print("最晚時間:", df['Time'].max())
    
    # 檢查 trades 數據
    print("\n=== BTC/USD Trades 數據 ===")
    for file in trades_files[:3]:  # 只顯示前3個文件
        print(f"\n文件: {Path(file).name}")
        df = pd.read_feather(file)
        print(f"數據條數: {len(df)}")
        print("\n列名:", df.columns.tolist())
        print("\n數據類型:")
        print(df.dtypes)
        print("\n前5行數據:")
        print(df.head())
        print("\n數據統計:")
        print(df.describe())
        
        # 顯示買賣方向統計
        if 'Side' in df.columns:
            print("\n買賣方向統計:")
            print(df['Side'].value_counts())
            
        # 檢查時間範圍
        if 'Time' in df.columns:
            print("\n時間範圍:")
            print("最早時間:", df['Time'].min())
            print("最晚時間:", df['Time'].max())

if __name__ == "__main__":
    check_data() 